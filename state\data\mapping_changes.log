{"timestamp": "2025-09-04T21:31:57.145498", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T21:32:31.317167", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T21:32:40.444422", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T21:48:17.733639", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T21:48:52.473008", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T21:57:23.911409", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T21:57:33.099089", "table_name": "mapping_config_退休人员工资表", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-04T21:58:18.621547", "table_name": "mapping_config_全部在职人员工资表", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T21:58:40.578198", "table_name": "mapping_config_全部在职人员工资表", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T23:46:37.866744", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:46:53.883669", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:46:55.441990", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:46:57.731497", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:46:58.591262", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:47:06.451813", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:47:14.493325", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T23:47:21.721547", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T23:47:24.211557", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T23:47:31.883470", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T23:47:41.251631", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T23:47:44.382128", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
