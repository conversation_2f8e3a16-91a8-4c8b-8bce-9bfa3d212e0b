# 数据格式显示不一致问题深度分析报告

## 问题描述

在"统一数据导入配置"窗口中，点击"离休人员工资表"后，在"预览验证"选项卡中发现：
- **护理费**：显示为"3,800.00"（有千位分隔符，两位小数）
- **离休补贴**：显示为"4770.0"（无千位分隔符，一位小数）

两个字段在"字段映射"选项卡中的"字段类型"都是"工资金额"，但显示格式不一致。

## 深度分析

### 1. 字段配置状态分析

根据配置文件 `state/data/field_mappings.json` 的分析：

#### 1.1 护理费配置
```json
"护理费": {
  "field_type": "salary_float",
  "data_type": "DECIMAL(10,2)",
  "is_required": false,
  "last_modified": 1756994249.0208411
}
```

#### 1.2 离休补贴配置
```json
"离休补贴": {
  "field_type": "salary_float", 
  "data_type": "DECIMAL(10,2)",
  "is_required": false,
  "last_modified": 1756994249.0051615
}
```

**结论**：两个字段的配置完全一致，都是 `salary_float` 类型。

### 2. 格式化引擎分析

#### 2.1 salary_float类型的默认配置
```python
# src/modules/data_import/formatting_engine.py 第396-405行
self._register_builtin_field_type("salary_float", {
    "name": "工资金额",
    "description": "金额数值，保留小数，千位分隔符",
    "rule_type": "number",
    "default_config": {
        "decimal_places": 2,
        "thousands_separator": True,
        "negative_format": "minus"
    }
})
```

#### 2.2 NumberFormattingRule的格式化逻辑
```python
# src/modules/data_import/formatting_engine.py 第76-80行
if thousands_separator:
    formatted = f"{num:,.{decimal_places}f}"
else:
    formatted = f"{num:.{decimal_places}f}"
```

**结论**：salary_float类型默认应该使用千位分隔符和两位小数。

### 3. 日志分析发现的关键问题

#### 3.1 护理费的处理过程
```
🔧 [修复] 字段 '护理费' 映射到类型: salary_float
🔧 [修复] 字段 '护理费' 格式化: '3800.0' -> '3,800.00'
```

#### 3.2 离休补贴的处理过程
```
🔧 [修复] 字段 '离休补贴' 无映射配置，使用原值
```

**关键发现**：虽然配置文件中两个字段都有相同的配置，但在实际运行时，系统报告"离休补贴"字段"无映射配置"。

### 4. 根本原因分析

#### 4.1 字段名称匹配问题
通过日志分析发现，问题出现在字段映射的动态加载过程中：

```python
# src/gui/unified_data_import_window.py 第5305-5337行
if field_mappings and header in field_mappings:
    field_config = field_mappings[header]
    field_type = field_config.get('field_type')
    # ... 格式化处理
else:
    formatted_value = str(value) if value is not None else ""
    if row == 0:
        self.logger.info(f"🔧 [修复] 字段 '{header}' 无映射配置，使用原值")
```

#### 4.2 配置加载时序问题
从日志中可以看到：
1. 系统在切换到"离休人员工资表"时会重新加载配置
2. 在某些情况下，字段映射配置可能没有完全加载到内存中
3. 导致部分字段被识别为"无映射配置"

#### 4.3 缓存同步问题
系统存在多个状态存储点：
- 配置文件状态
- 内存缓存状态  
- 格式化引擎状态
- UI显示状态

这些状态之间可能存在同步延迟或不一致。

### 5. 技术层面的具体原因

#### 5.1 字段映射加载机制
```python
# 从日志可以看出加载过程
🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
```

系统使用edit_history来提取最新配置，但这个过程可能存在时序问题。

#### 5.2 格式化引擎调用差异
- **护理费**：成功获取到字段类型，调用格式化引擎
- **离休补贴**：未获取到字段类型，直接使用原值

#### 5.3 数据类型处理差异
- **护理费**：原始值 `3800.0` → 格式化为 `3,800.00`
- **离休补贴**：原始值 `4770.0` → 直接显示为 `4770.0`

### 6. 影响范围评估

这个问题可能影响：
1. 所有使用动态字段映射的表格
2. 在配置加载过程中的字段显示
3. 用户对数据一致性的信任度

### 7. 解决方案建议

#### 7.1 立即修复方案
1. **强化字段映射加载机制**：确保所有字段配置都能正确加载
2. **添加重试机制**：在字段映射失败时进行重试
3. **统一格式化入口**：确保所有字段都经过格式化引擎处理

#### 7.2 长期优化方案
1. **重构配置同步机制**：建立统一的配置状态管理
2. **增强错误处理**：提供更详细的错误信息和恢复机制
3. **添加配置验证**：在加载配置时进行完整性验证

## 结论

这个问题的根本原因是**字段映射配置的动态加载过程中存在时序问题**，导致部分字段无法正确获取到配置信息，从而绕过了格式化引擎的处理。虽然配置文件中的设置是正确的，但在运行时的配置加载机制存在缺陷。

需要重点关注配置加载的时序和同步机制，确保所有字段都能获得一致的格式化处理。
