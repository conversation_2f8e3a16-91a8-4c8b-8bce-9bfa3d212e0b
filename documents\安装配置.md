
其他：
https://nf.video/


https://cursor.com/cli



<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
yulzvuxp755
yulzvuxp755
<EMAIL>
gmail800847L<PERSON><PERSON>ro
---
KIRO-vapC-giYK

我有一个疑问：
你能够根据项目代码现状，回推项目的需求文档、设计文档、任务文档吗？



Roo
---
kimi k2
api.moonshot.ai
api.moonshot.cn

sk-ODY1btiSqrSLOfovbSFTZmvRuC962h8NON6jGaxRwB1d8D65

models:
kimi-k2-0711-preview


bigmodel GLM4.5

models:
glm-4.5

https://open.bigmodel.cn/api/anthropic

8129d486ab034ca58aea1911897c44b9.ifiYkkeuwW3ZmDfd



Claude Code
---
官网文档：https://docs.anthropic.com/zh-CN/docs/claude-code/settings
CC 更新：https://youmind.site/F05CLPXvcIoLKp
Zen MCP：https://github.com/BeehiveInnovations/zen-mcp-server
CC WSL 指南：https://gxokrwbnshq.feishu.cn/wiki/COhdwrdxJigYqckl5o3c6vEJnwd
GAC平台：https://gaccode.com/signup?ref=UWDADYQI

npm install -g https://gaccode.com/claudecode/install --registry=https://registry.npmmirror.com



##### set env vars
export ANTHROPIC_BASE_URL=https://relay01.gaccode.com/claudecode
export ANTHROPIC_API_KEY=sk-ant-oat01-46f45413acfda6ff4e26a2830acdc5692ed3c1ca6b0ab7d37a7a669308f2a0b4


##### programmatically approve this API Key
(cat ~/.claude.json 2>/dev/null || echo 'null') | jq --arg key "${ANTHROPIC_API_KEY: -20}" '(. // {}) | .customApiKeyResponses.approved |= ([.[]?, $key] | unique)' > ~/.claude.json.tmp && mv ~/.claude.json.tmp ~/.claude.json

通用兼容性：✓ 适用于 CI/CD 流水线、GitHub Actions 和本地开发 • ✓ 兼容官方 @anthropic-ai/claude-code 包 • ✓ 标准环境变量格式




1.环境准备
- ClaudeCode官方目前原生支持Windows。
- 安装并配置好Git for Windows，确保Node.js环境可用。
下载安装：https://git-scm.com/download/win
或使用winget：winget install --id Git.Git -e --source winget



2.安装Claude Code
在终端中执行：
npm install -g @anthropic-ai/claude-code
安装完成后可通过claude --version验证。


3.获取Kimi K2 APIKey
可以在Claude Code中使用它！用它来取代Opus和Sonnet
platform.moonshot.ai/docs/guide/agent-support
https://platform.moonshot.cn/docs/guide/agent-support
- 在平台注册
- 获取API密钥
- export ANTHROPIC_AUTH_TOKEN=sk-YOURKEY
- export ANTHROPIC_BASE_URL=api.moonshot.ai/anthropic


4.配置环境变量
ClaudeCode通过环境变量来指定APIKey和请求地址。具体操作如下：
Linux跟Mac:
export ANTHROPIC_BASE_URL=https://api.moonshot.cn/anthropic/
export ANTHROPIC_API_KEY=YOUR_API_KEY_HERE

Windows powershell:
$env:ANTHROPIC_BASE_URL = "https://api.moonshot.cn/anthropic/"
$env:ANTHROPIC_API_KEY = "sk-cB5mZn2jvNM0PrdDZ0sJiMK7gtlDqaKAk0aXm7297j9yL5C4"

Claude启动


---

/output-style:new 实现由 Claude Code 根据用户的需求编写代码，然后调用 Gemini CLI 对代码进行 code review 并给出改进建议，
再由 Claude Code 根据 Gemini CLI 给出的改进建议对代码进行改进和优化。 Gemini CLI 的示例如下：
gemini -p "Explain the architecture of this codebase"





## Claude Code Router

npm install -g @anthropic-ai/claude-code

npm install -g @musistudio/claude-code-router


