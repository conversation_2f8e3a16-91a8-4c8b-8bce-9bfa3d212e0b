2025-09-04 23:45:19.230 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-04 23:45:19.230 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-04 23:45:19.230 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-04 23:45:19.230 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-04 23:45:19.230 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-04 23:45:19.230 | INFO     | src:<module>:20 | 月度工资异动处理系统 v2.0.0-refactored 初始化完成
2025-09-04 23:45:22.293 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-04 23:45:22.293 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-04 23:45:22.293 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-04 23:45:22.309 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-04 23:45:22.309 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-04 23:45:22.309 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-04 23:45:22.309 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 23:45:22.309 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 23:45:22.309 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 23:45:22.309 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 23:45:22.309 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-04 23:45:22.309 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-04 23:45:22.309 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-04 23:45:22.324 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 23:45:22.324 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-04 23:45:22.324 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-04 23:45:22.324 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-04 23:45:22.324 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-04 23:45:22.324 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 23:45:22.324 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-04 23:45:22.340 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-04 23:45:22.340 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-04 23:45:22.340 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-04 23:45:22.340 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11903 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-04 23:45:22.340 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 23:45:22.340 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11758 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-04 23:45:22.340 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11796 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-04 23:45:22.531 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-04 23:45:22.531 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-04 23:45:22.531 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 23:45:22.546 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 23:45:22.546 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 23:45:22.546 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 23:45:22.546 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 23:45:22.546 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-09-04 23:45:22.562 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 23:45:22.562 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 23:45:22.562 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-04 23:45:22.562 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-09-04 23:45:22.562 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 23:45:22.562 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 23:45:22.562 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 23:45:22.562 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 23:45:22.578 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-04 23:45:22.578 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 47.0ms
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-09-04 23:45:22.593 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-09-04 23:45:22.609 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-04 23:45:22.609 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-09-04 23:45:22.609 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-04 23:45:22.609 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-04 23:45:22.609 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-04 23:45:22.850 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-09-04 23:45:22.850 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-09-04 23:45:22.850 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-04 23:45:22.850 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-09-04 23:45:22.850 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-09-04 23:45:22.850 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-04 23:45:22.866 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-04 23:45:22.866 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-09-04 23:45:22.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-09-04 23:45:22.866 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 23:45:22.866 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 23:45:22.866 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 23:45:22.866 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-04 23:45:22.866 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-04 23:45:22.866 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-04 23:45:22.898 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.898 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.898 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1821 | 动态加载了 1 个月份的工资数据导航
2025-09-04 23:45:22.898 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.898 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-04 23:45:22.898 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > unknown', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-04 23:45:22.898 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 3个展开项
2025-09-04 23:45:22.913 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > unknown', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-04 23:45:22.949 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-04 23:45:22.953 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-04 23:45:22.954 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-04 23:45:22.957 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-04 23:45:22.963 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.969 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.971 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-04 23:45:22.973 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-04 23:45:22.982 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-04 23:45:22.983 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-04 23:45:22.986 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 23:45:22.987 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-04 23:45:22.989 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-04 23:45:23.229 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-04 23:45:23.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-04 23:45:23.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-09-04 23:45:23.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-09-04 23:45:23.265 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-04 23:45:23.266 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-04 23:45:23.271 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-04 23:45:23.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-04 23:45:23.274 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-04 23:45:23.275 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 23:45:23.276 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 23:45:23.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-04 23:45:23.297 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-09-04 23:45:23.298 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-04 23:45:23.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-04 23:45:23.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-04 23:45:23.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-04 23:45:23.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-09-04 23:45:23.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-04 23:45:23.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-04 23:45:23.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-09-04 23:45:23.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-04 23:45:23.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-04 23:45:23.317 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 23:45:23.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 23:45:23.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 23:45:23.332 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-04 23:45:23.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-09-04 23:45:23.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-04 23:45:23.336 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 23:45:23.344 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-04 23:45:23.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-04 23:45:23.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 23:45:23.362 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 23:45:23.363 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 23:45:23.364 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 23:45:23.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 23:45:23.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-09-04 23:45:23.368 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-04 23:45:23.383 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 44.2ms
2025-09-04 23:45:23.383 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 23:45:23.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 23:45:23.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 23:45:23.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 23:45:23.391 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 23:45:23.400 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-04 23:45:23.467 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-04 23:45:23.468 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-04 23:45:23.517 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-09-04 23:45:23.566 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-09-04 23:45:23.567 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-09-04 23:45:23.570 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-04 23:45:23.571 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-04 23:45:23.572 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-09-04 23:45:23.573 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-09-04 23:45:23.574 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-04 23:45:23.575 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-09-04 23:45:23.584 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6935 | 🔧 [P1-2修复] 发现 7 个表的配置
2025-09-04 23:45:23.601 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:549 |  [配置提取] 从 field_mappings 构造字段配置: salary_data_2025_05
2025-09-04 23:45:23.609 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:45:23.610 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:45:23.611 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:45:23.611 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:45:23.612 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:45:23.613 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:45:23.614 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:45:23.615 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:45:23.616 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:45:23.618 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:45:23.627 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:45:23.628 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:45:23.629 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:45:23.629 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:45:23.630 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:45:23.632 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:45:23.633 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:45:23.634 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:45:23.635 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:45:23.636 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:45:23.644 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:45:23.645 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:45:23.646 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:45:23.648 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:45:23.654 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:45:23.669 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:45:23.670 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:45:23.670 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:45:23.671 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:45:23.672 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:45:23.673 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:45:23.673 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:45:23.674 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:45:23.675 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:45:23.676 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:45:23.680 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:45:23.730 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:45:23.731 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:45:23.732 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:45:23.733 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:45:23.734 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:45:23.735 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:45:23.735 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:45:23.744 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 27 个字段的最新配置
2025-09-04 23:45:23.744 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: text_string
2025-09-04 23:45:23.746 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: code_string
2025-09-04 23:45:23.746 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:45:23.747 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:45:23.748 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:45:23.749 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本退休费' 最新类型: salary_float
2025-09-04 23:45:23.749 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:45:23.750 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:45:23.751 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离退休生活补贴' 最新类型: salary_float
2025-09-04 23:45:23.752 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:45:23.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:45:23.758 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:45:23.759 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增资预付' 最新类型: text_string
2025-09-04 23:45:23.759 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2016待遇调整' 最新类型: text_string
2025-09-04 23:45:23.760 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2017待遇调整' 最新类型: text_string
2025-09-04 23:45:23.761 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2018待遇调整' 最新类型: text_string
2025-09-04 23:45:23.762 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2019待遇调整' 最新类型: text_string
2025-09-04 23:45:23.762 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2020待遇调整' 最新类型: text_string
2025-09-04 23:45:23.763 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2021待遇调整' 最新类型: text_string
2025-09-04 23:45:23.764 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2022待遇调整' 最新类型: text_string
2025-09-04 23:45:23.766 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2023待遇调整' 最新类型: text_string
2025-09-04 23:45:23.766 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: text_string
2025-09-04 23:45:23.778 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: text_string
2025-09-04 23:45:23.778 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:45:23.779 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '公积' 最新类型: text_string
2025-09-04 23:45:23.780 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: text_string
2025-09-04 23:45:23.781 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: text_string
2025-09-04 23:45:23.782 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:45:23.782 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_退休人员工资表, 字段数: 27
2025-09-04 23:45:23.792 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 2 个字段的最新配置
2025-09-04 23:45:23.792 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:45:23.793 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:45:23.795 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:45:23.796 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_全部在职人员工资表, 字段数: 2
2025-09-04 23:45:23.798 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6945 | ✅ [P1-2修复] 已加载字段映射信息，共5个表的映射
2025-09-04 23:45:23.807 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 23:45:23.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 23:45:23.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 23:45:23.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 23:45:23.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 23:45:23.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-09-04 23:45:23.817 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 23:45:23.817 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 23:45:23.819 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 23:45:23.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 23:45:23.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 6.8ms
2025-09-04 23:45:23.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 23:45:23.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 23:45:23.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 23:45:23.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 23:45:23.834 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 23:45:23.835 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-04 23:45:23.835 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8637 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-04 23:45:23.836 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 23:45:23.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 23:45:23.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 23:45:23.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 23:45:23.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 23:45:23.845 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 23:45:23.846 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 23:45:23.847 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 23:45:23.848 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 23:45:23.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.7ms
2025-09-04 23:45:23.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 23:45:23.850 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 23:45:23.858 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 23:45:23.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 23:45:23.879 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 23:45:23.882 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8655 | 已显示标准空表格，表头数量: 22
2025-09-04 23:45:23.883 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-09-04 23:45:24.002 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-04 23:45:24.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 23:45:24.013 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-09-04 23:45:24.014 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-04 23:45:24.016 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 23:45:24.020 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-04 23:45:24.022 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-04 23:45:24.025 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 23:45:24.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 23:45:24.198 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-04 23:45:24.198 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-09-04 23:45:24.699 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9539 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-04 23:45:24.700 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9449 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-04 23:45:24.704 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9463 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-04 23:45:24.704 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9997 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-04 23:45:24.722 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9469 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-04 23:46:21.405 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-04 23:46:21.405 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8460 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-04 23:46:21.405 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-04 23:46:21.624 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 23:46:21.624 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 23:46:21.624 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 23:46:21.624 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 23:46:21.624 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-04 23:46:21.640 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-04 23:46:21.640 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-04 23:46:21.640 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 23:46:21.640 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-04 23:46:21.655 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-04 23:46:21.702 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-09-04 23:46:21.702 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-09-04 23:46:21.702 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-09-04 23:46:21.702 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-09-04 23:46:21.780 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-04 23:46:21.780 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 23:46:21.796 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-04 23:46:21.842 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 23:46:21.842 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-04 23:46:21.842 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-04 23:46:21.858 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 23:46:21.858 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 23:46:21.858 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 23:46:21.858 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 23:46:21.858 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 23:46:21.858 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 23:46:21.858 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 23:46:21.858 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 23:46:21.858 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 23:46:21.858 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 23:46:21.858 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 23:46:21.858 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-04 23:46:21.858 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 15.7ms
2025-09-04 23:46:21.952 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 23:46:21.952 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: number
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: string
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: date
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: code
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: custom
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: salary_float - 工资金额
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: employee_id_string - 工号
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: name_string - 姓名
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: date_string - 日期
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: id_number_string - 身份证号
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: code_string - 代码
2025-09-04 23:46:21.969 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: float - 浮点数
2025-09-04 23:46:21.983 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: integer - 整数
2025-09-04 23:46:21.983 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: text_string - 文本字符串
2025-09-04 23:46:21.983 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: personnel_category_code - 人员类别代码
2025-09-04 23:46:21.983 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 23:46:21.983 | INFO     | src.modules.data_import.formatting_engine:reload_custom_field_types:825 | 🔧 [方案一实施] 重新加载了 0 个自定义字段类型
2025-09-04 23:46:22.259 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-04 23:46:22.260 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:46:22.263 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:46:22.264 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:22.375 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 23:46:22.378 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:22.380 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:46:22.381 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-04 23:46:22.385 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 23:46:22.386 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 23:46:22.392 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-04 23:46:22.393 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:22.498 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:22.500 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-04 23:46:22.504 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:22.611 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:22.613 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-04 23:46:22.616 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:22.717 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:22.719 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 23:46:26.282 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-04 23:46:26.298 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 23:46:26.298 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-04 23:46:26.314 | INFO     | src.modules.data_import.config_template_manager:_load_templates:579 | 加载了 4 个模板
2025-09-04 23:46:26.314 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-09-04 23:46:26.314 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-09-04 23:46:26.314 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-04 23:46:26.331 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-04 23:46:26.331 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:46:26.331 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:46:26.331 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:26.438 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 23:46:26.438 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:26.438 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:46:26.438 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-04 23:46:26.438 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:46:26.454 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:46:26.470 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:46:26.970 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:46:27.079 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:46:27.079 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:46:27.095 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 23:46:27.095 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 23:46:37.866 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:46:37.913 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:46:37.927 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:46:37.927 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:46:37.945 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:46:37.945 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:46:37.990 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:46:38.005 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:46:38.005 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:46:53.884 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:46:55.458 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:46:57.731 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:46:58.591 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:46:58.637 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:46:58.637 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:46:58.637 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:46:58.637 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:46:58.637 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:46:58.653 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:46:58.672 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:46:58.672 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:46:58.672 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:46:58.672 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:46:58.716 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:46:58.731 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:46:58.731 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:46:58.747 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:47:06.451 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:07.874 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:07.890 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.906 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:07.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:07.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:07.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:07.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:07.953 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:07.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:07.984 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.000 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.015 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.031 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.046 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.062 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.093 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.109 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.124 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.140 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.156 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.171 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.187 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.202 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.202 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.218 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.234 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.234 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.234 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.249 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.265 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.281 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.296 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.312 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.327 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.327 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.327 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.374 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.序号
2025-09-04 23:47:08.406 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.人员代码
2025-09-04 23:47:08.453 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.姓名
2025-09-04 23:47:08.499 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.部门名称
2025-09-04 23:47:08.531 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.基本离休费
2025-09-04 23:47:08.562 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.结余津贴
2025-09-04 23:47:08.593 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.生活补贴
2025-09-04 23:47:08.640 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.住房补贴
2025-09-04 23:47:08.672 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.物业补贴
2025-09-04 23:47:08.718 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.离休补贴
2025-09-04 23:47:08.749 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.护理费
2025-09-04 23:47:08.781 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.增发一次性生活补贴
2025-09-04 23:47:08.820 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.补发
2025-09-04 23:47:08.851 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.合计
2025-09-04 23:47:08.898 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.借支
2025-09-04 23:47:08.930 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.备注
2025-09-04 23:47:08.930 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-04 23:47:08.945 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.945 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.945 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.945 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.945 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:08.961 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:08.977 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:08.992 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.008 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.008 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.008 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.008 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.008 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.023 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.039 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.055 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.070 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.070 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.070 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.086 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.102 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.102 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.102 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.117 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.159 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.164 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.180 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.180 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.195 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.211 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.227 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.243 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.243 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.243 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.258 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.274 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.289 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.304 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.304 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.304 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.304 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.304 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.320 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.337 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.351 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.367 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:09.383 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:09.399 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:09.445 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.序号
2025-09-04 23:47:09.476 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.人员代码
2025-09-04 23:47:09.524 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.姓名
2025-09-04 23:47:09.555 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.部门名称
2025-09-04 23:47:09.617 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.基本离休费
2025-09-04 23:47:09.648 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.结余津贴
2025-09-04 23:47:09.680 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.生活补贴
2025-09-04 23:47:09.711 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.住房补贴
2025-09-04 23:47:09.758 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.物业补贴
2025-09-04 23:47:09.789 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.离休补贴
2025-09-04 23:47:09.836 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.护理费
2025-09-04 23:47:09.867 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.增发一次性生活补贴
2025-09-04 23:47:09.899 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.补发
2025-09-04 23:47:09.945 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.合计
2025-09-04 23:47:09.976 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.借支
2025-09-04 23:47:10.008 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_离休人员工资表.备注
2025-09-04 23:47:10.008 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 23:47:10.008 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-04 23:47:10.023 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-04 23:47:10.023 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-04 23:47:10.023 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:47:10.023 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:47:10.023 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-04 23:47:10.133 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:10.149 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:10.149 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:10.149 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:10.695 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:10.805 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:10.817 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 23:47:10.817 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 23:47:10.833 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-04 23:47:14.493 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_A岗职工
2025-09-04 23:47:14.507 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:14.507 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 23:47:14.507 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:14.507 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:14.507 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 23:47:14.522 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 23:47:14.539 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 23:47:14.539 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 23:47:14.554 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 23:47:21.721 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_A岗职工
2025-09-04 23:47:23.659 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:47:23.659 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:47:23.659 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:23.768 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 23:47:23.768 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:23.768 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 23:47:23.768 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 10行 x 21列
2025-09-04 23:47:23.768 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 10行 × 21列
2025-09-04 23:47:24.211 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_A岗职工
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 23:47:24.224 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 23:47:24.240 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 23:47:24.272 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:24.272 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 23:47:24.272 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:24.272 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:24.272 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 23:47:24.287 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 23:47:31.883 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_A岗职工
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.694 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.694 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.694 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.710 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.726 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.726 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.726 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.741 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.741 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.741 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.757 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.757 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.757 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.772 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.772 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.772 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.790 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.790 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.790 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.804 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.804 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.804 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.819 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.835 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.835 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.835 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.835 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.835 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.851 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.866 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.866 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.875 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.891 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.891 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.891 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.891 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.907 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.922 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.922 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.922 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.955 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.955 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.955 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.955 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.955 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.985 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.985 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:34.985 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:34.985 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:34.985 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.016 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.016 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.047 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.047 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.047 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.079 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.079 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.079 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.095 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.095 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.095 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.141 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.序号
2025-09-04 23:47:35.175 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.工号
2025-09-04 23:47:35.204 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.姓名
2025-09-04 23:47:35.251 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.部门名称
2025-09-04 23:47:35.282 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.人员类别
2025-09-04 23:47:35.329 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.人员类别代码
2025-09-04 23:47:35.360 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年岗位工资
2025-09-04 23:47:35.407 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年校龄工资
2025-09-04 23:47:35.438 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.津贴
2025-09-04 23:47:35.469 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.结余津贴
2025-09-04 23:47:35.501 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年基础性绩效
2025-09-04 23:47:35.547 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.卫生费
2025-09-04 23:47:35.578 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年生活补贴
2025-09-04 23:47:35.625 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.车补
2025-09-04 23:47:35.657 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年奖励性绩效预发
2025-09-04 23:47:35.704 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.补发
2025-09-04 23:47:35.751 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.借支
2025-09-04 23:47:35.782 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.应发工资
2025-09-04 23:47:35.813 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025公积金
2025-09-04 23:47:35.860 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.保险扣款
2025-09-04 23:47:35.891 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.代扣代存养老保险
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.907 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.923 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.923 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.923 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.923 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.923 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.938 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.938 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.938 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.938 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.938 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.953 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.953 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:35.969 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:35.969 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:35.969 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.001 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.001 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.001 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.016 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.016 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.016 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.032 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.032 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.032 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.048 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.063 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.063 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.063 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.079 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.079 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.079 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.094 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.094 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.094 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.110 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.110 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.125 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.125 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.125 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.141 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.157 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.157 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.157 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.172 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.172 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.172 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.188 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.188 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.188 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.204 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.219 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.219 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.219 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.219 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.219 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.235 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.235 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.252 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.252 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.252 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.266 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.266 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.266 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.282 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.282 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.282 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.297 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.297 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.297 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.297 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.297 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.313 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.329 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.329 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 21 个字段的最新配置
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别' 最新类型: text_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '车补' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '工号' 最新类型: employee_id_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:36.329 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员类别代码' 最新类型: personnel_category_code
2025-09-04 23:47:36.344 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年岗位工资' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年校龄工资' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '津贴' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年基础性绩效' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '卫生费' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年生活补贴' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025年奖励性绩效预发' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '应发工资' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '2025公积金' 最新类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '保险扣款' 最新类型: salary_float
2025-09-04 23:47:36.360 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '保险扣款' 类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '代扣代存养老保险' 最新类型: salary_float
2025-09-04 23:47:36.360 | WARNING  | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:653 | 🔧 [方案3修复] 重点字段 '代扣代存养老保险' 类型: salary_float
2025-09-04 23:47:36.360 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 23:47:36.407 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.序号
2025-09-04 23:47:36.438 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.工号
2025-09-04 23:47:36.485 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.姓名
2025-09-04 23:47:36.516 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.部门名称
2025-09-04 23:47:36.563 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.人员类别
2025-09-04 23:47:36.594 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.人员类别代码
2025-09-04 23:47:36.673 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年岗位工资
2025-09-04 23:47:36.797 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年校龄工资
2025-09-04 23:47:36.845 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.津贴
2025-09-04 23:47:36.877 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.结余津贴
2025-09-04 23:47:36.907 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年基础性绩效
2025-09-04 23:47:36.954 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.卫生费
2025-09-04 23:47:37.001 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年生活补贴
2025-09-04 23:47:37.047 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.车补
2025-09-04 23:47:37.079 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025年奖励性绩效预发
2025-09-04 23:47:37.129 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.补发
2025-09-04 23:47:37.157 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.借支
2025-09-04 23:47:37.203 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.应发工资
2025-09-04 23:47:37.241 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.2025公积金
2025-09-04 23:47:37.282 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.保险扣款
2025-09-04 23:47:37.314 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1150 | 💾 [即时保存] 字段配置保存成功: mapping_config_A岗职工.代扣代存养老保险
2025-09-04 23:47:37.314 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 23:47:37.314 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-04 23:47:37.314 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-04 23:47:37.329 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:47:37.329 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:47:37.329 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:37.423 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 23:47:37.423 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:37.423 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:47:37.423 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-04 23:47:37.423 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:645 | 🔧 [方案3] 从edit_history提取到 16 个字段的最新配置
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '序号' 最新类型: integer
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '人员代码' 最新类型: employee_id_string
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '姓名' 最新类型: name_string
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '部门名称' 最新类型: text_string
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '基本离休费' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '结余津贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '生活补贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '住房补贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '物业补贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '离休补贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '护理费' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '增发一次性生活补贴' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '补发' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '合计' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '借支' 最新类型: salary_float
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_latest_configs_from_history:650 | 🔧 [方案3修复] 字段 '备注' 最新类型: text_string
2025-09-04 23:47:37.438 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:518 | 🔧 [方案3] 从edit_history提取最新配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 23:47:37.880 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:37.991 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:37.992 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:47:37.994 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 23:47:37.995 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 23:47:41.039 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 23:47:41.041 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 23:47:41.041 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 23:47:41.142 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 23:47:41.144 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 23:47:41.145 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 23:47:41.146 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-04 23:47:41.148 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 23:47:41.148 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 23:47:41.149 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-04 23:47:41.180 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:41.181 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:47:41.181 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:41.182 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:41.182 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:47:41.183 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:41.183 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:47:41.184 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:47:41.203 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:47:41.203 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:47:41.204 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:47:41.204 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:47:41.205 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:41.205 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:47:41.205 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:41.206 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:47:41.252 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:47:41.276 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:41.276 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:47:41.276 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:41.277 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:41.277 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:47:41.277 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:41.278 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:47:41.278 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:47:41.279 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:47:41.281 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:47:41.281 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:47:41.282 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:47:41.282 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:41.294 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:47:41.294 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:41.295 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:47:41.327 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 23:47:41.328 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 23:47:41.328 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 23:47:41.329 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 23:47:41.329 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 23:47:41.329 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 23:47:41.330 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 23:47:41.330 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 23:47:41.330 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 23:47:41.331 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 23:47:41.331 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 23:47:41.331 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 23:47:41.332 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 23:47:41.332 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 23:47:41.333 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 23:47:41.342 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 23:47:44.382 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_离休人员工资表
2025-09-04 23:47:46.917 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6009 | 用户取消了数据导入
2025-09-04 23:47:50.042 | INFO     | __main__:main:519 | 应用程序正常退出
