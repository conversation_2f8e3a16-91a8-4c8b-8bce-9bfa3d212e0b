# -*- coding: utf-8 -*-
"""
快速修复方案测试脚本
测试数据格式显示不一致问题的修复效果
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.core.config_monitor import ConfigMonitor, ConfigConsistencyMonitor
from src.modules.data_import.formatting_engine import get_formatting_engine


class TestQuickFixSolution(unittest.TestCase):
    """快速修复方案测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config_data = {
            "table_mappings": {
                "mapping_config_离休人员工资表": {
                    "field_configs": {
                        "护理费": {
                            "field_type": "salary_float",
                            "data_type": "DECIMAL(10,2)",
                            "is_required": False
                        },
                        "离休补贴": {
                            "field_type": "salary_float",
                            "data_type": "DECIMAL(10,2)",
                            "is_required": False
                        },
                        "基本离休费": {
                            "field_type": "salary_float",
                            "data_type": "DECIMAL(10,2)",
                            "is_required": False
                        }
                    }
                }
            }
        }
        
        self.test_data = [
            {
                "护理费": "3800.0",
                "离休补贴": "4770.0",
                "基本离休费": "5200.0"
            }
        ]

    def test_field_mapping_retry_mechanism(self):
        """测试字段映射重试机制"""
        print("\n🔄 测试字段映射重试机制...")
        
        # 模拟窗口实例
        with patch('PyQt5.QtWidgets.QDialog'):
            window = UnifiedDataImportWindow()
            
            # 模拟配置加载失败然后成功的场景
            def mock_load_internal(table_name):
                if not hasattr(mock_load_internal, 'call_count'):
                    mock_load_internal.call_count = 0
                mock_load_internal.call_count += 1
                
                if mock_load_internal.call_count == 1:
                    # 第一次调用失败
                    return {}
                else:
                    # 第二次调用成功
                    return self.test_config_data["table_mappings"]["mapping_config_离休人员工资表"]["field_configs"]
            
            window._load_field_mappings_internal = mock_load_internal
            
            # 测试重试机制
            result = window._load_field_mappings_with_retry("mapping_config_离休人员工资表")
            
            # 验证结果
            self.assertIsNotNone(result)
            self.assertIn("护理费", result)
            self.assertIn("离休补贴", result)
            self.assertEqual(result["护理费"]["field_type"], "salary_float")
            self.assertEqual(result["离休补贴"]["field_type"], "salary_float")
            
            print("✅ 字段映射重试机制测试通过")

    def test_field_type_inference(self):
        """测试字段类型推断"""
        print("\n🔍 测试字段类型推断...")
        
        with patch('PyQt5.QtWidgets.QDialog'):
            window = UnifiedDataImportWindow()
            
            # 测试工资相关字段推断
            self.assertEqual(window._infer_field_type_from_name("护理费"), "salary_float")
            self.assertEqual(window._infer_field_type_from_name("离休补贴"), "salary_float")
            self.assertEqual(window._infer_field_type_from_name("基本工资"), "salary_float")
            
            # 测试其他类型字段推断
            self.assertEqual(window._infer_field_type_from_name("人员代码"), "employee_id")
            self.assertEqual(window._infer_field_type_from_name("姓名"), "name_string")
            self.assertEqual(window._infer_field_type_from_name("入职日期"), "date_field")
            
            print("✅ 字段类型推断测试通过")

    def test_numeric_field_detection(self):
        """测试数值字段检测"""
        print("\n🔢 测试数值字段检测...")
        
        with patch('PyQt5.QtWidgets.QDialog'):
            window = UnifiedDataImportWindow()
            
            # 测试数值字段
            self.assertTrue(window._is_numeric_field("护理费"))
            self.assertTrue(window._is_numeric_field("离休补贴"))
            self.assertTrue(window._is_numeric_field("基本工资"))
            self.assertTrue(window._is_numeric_field("合计金额"))
            
            # 测试非数值字段
            self.assertFalse(window._is_numeric_field("姓名"))
            self.assertFalse(window._is_numeric_field("部门名称"))
            self.assertFalse(window._is_numeric_field("备注"))
            
            print("✅ 数值字段检测测试通过")

    def test_fallback_mappings(self):
        """测试兜底配置生成"""
        print("\n🛡️ 测试兜底配置生成...")
        
        with patch('PyQt5.QtWidgets.QDialog'):
            window = UnifiedDataImportWindow()
            
            # 测试离休表兜底配置
            fallback = window._get_fallback_mappings("mapping_config_离休人员工资表")
            
            self.assertIsNotNone(fallback)
            self.assertIn("护理费", fallback)
            self.assertIn("离休补贴", fallback)
            self.assertEqual(fallback["护理费"]["field_type"], "salary_float")
            self.assertEqual(fallback["离休补贴"]["field_type"], "salary_float")
            
            print("✅ 兜底配置生成测试通过")

    def test_enhanced_format_preview_value(self):
        """测试增强的预览值格式化"""
        print("\n🎨 测试增强的预览值格式化...")
        
        with patch('PyQt5.QtWidgets.QDialog'):
            window = UnifiedDataImportWindow()
            
            # 模拟格式化引擎
            mock_engine = Mock()
            mock_engine.format_value.return_value = "3,800.00"
            
            with patch('src.modules.data_import.formatting_engine.get_formatting_engine', return_value=mock_engine):
                # 测试有映射配置的情况
                field_mappings = {
                    "护理费": {
                        "field_type": "salary_float",
                        "data_type": "DECIMAL(10,2)",
                        "is_required": False
                    }
                }
                
                result = window._format_preview_value_enhanced("3800.0", "护理费", field_mappings, 0)
                self.assertEqual(result, "3,800.00")
                
                # 测试无映射配置但可推断类型的情况
                result = window._format_preview_value_enhanced("4770.0", "离休补贴", {}, 0)
                self.assertEqual(result, "3,800.00")  # 模拟格式化结果
                
                print("✅ 增强的预览值格式化测试通过")

    def test_config_monitor(self):
        """测试配置监控器"""
        print("\n🔔 测试配置监控器...")
        
        # 创建临时配置文件
        import tempfile
        import json
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config_data, f)
            temp_config_path = f.name
        
        try:
            # 创建配置监控器
            monitor = ConfigMonitor(temp_config_path)
            
            # 测试回调机制
            callback_called = False
            callback_data = None
            
            def test_callback(data):
                nonlocal callback_called, callback_data
                callback_called = True
                callback_data = data
            
            monitor.add_callback(test_callback)
            
            # 强制检查（模拟文件变更）
            monitor.force_check()
            
            # 验证回调是否被调用
            self.assertTrue(callback_called)
            self.assertIsNotNone(callback_data)
            
            print("✅ 配置监控器测试通过")
            
        finally:
            # 清理临时文件
            os.unlink(temp_config_path)

    def test_consistency_monitor(self):
        """测试配置一致性监控"""
        print("\n🔍 测试配置一致性监控...")
        
        # 创建临时配置文件
        import tempfile
        import json
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config_data, f)
            temp_config_path = f.name
        
        try:
            # 创建一致性监控器
            consistency_monitor = ConfigConsistencyMonitor(temp_config_path)
            
            # 测试字段类型一致性检查
            issues = consistency_monitor.check_field_type_consistency("mapping_config_离休人员工资表")
            
            # 由于所有字段都是salary_float类型，应该没有一致性问题
            print(f"一致性检查结果: {issues}")
            
            print("✅ 配置一致性监控测试通过")
            
        finally:
            # 清理临时文件
            os.unlink(temp_config_path)

    def test_formatting_consistency(self):
        """测试格式化一致性"""
        print("\n🎯 测试格式化一致性...")
        
        # 获取格式化引擎
        formatting_engine = get_formatting_engine()
        
        # 测试相同类型字段的格式化结果
        test_values = ["3800.0", "4770.0", "5200.0"]
        formatted_results = []
        
        for value in test_values:
            try:
                formatted = formatting_engine.format_value(value, "salary_float")
                formatted_results.append(formatted)
                print(f"  {value} -> {formatted}")
            except Exception as e:
                print(f"  格式化失败: {value} -> {e}")
                formatted_results.append(str(value))
        
        # 检查格式化结果的一致性特征
        for result in formatted_results:
            # 检查是否有千位分隔符
            has_comma = "," in result
            # 检查小数位数
            if "." in result:
                decimal_places = len(result.split(".")[-1])
            else:
                decimal_places = 0
            
            print(f"  {result}: 千位分隔符={has_comma}, 小数位数={decimal_places}")
        
        print("✅ 格式化一致性测试完成")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行快速修复方案测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestQuickFixSolution)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 所有测试通过！快速修复方案验证成功！")
    else:
        print("❌ 部分测试失败，需要进一步调试")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"详情: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"详情: {error[1]}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
