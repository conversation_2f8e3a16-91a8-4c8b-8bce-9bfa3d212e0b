{"version": "2.0", "last_updated": "2025-09-04T23:47:44.350872", "global_settings": {"auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "preserve_chinese_headers": true}, "table_mappings": {"salary_data_2025_07_active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code", "2025年岗位工资": "position_salary_2025", "2025年薪级工资": "grade_salary_2025", "津贴": "allowance", "结余津贴": "balance_allowance", "应发工资": "total_salary"}, "active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code"}, "salary_data_2025_05": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-04T20:13:38.768639", "table_type": "unknown"}}, "mapping_config_A岗职工": {"metadata": {"created_at": "2025-09-04T20:13:55.796530", "last_modified": "2025-09-04T23:47:37.282675", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "field_2025年岗位工资", "2025年校龄工资": "field_2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "field_2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "field_2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "field_2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "field_2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-09-04T20:13:55.796530", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988035.79653}, "user_action": true}, {"timestamp": "2025-09-04T20:13:56.296842", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988036.2968428}, "user_action": true}, {"timestamp": "2025-09-04T20:13:59.624909", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988039.6249094}, "user_action": true}, {"timestamp": "2025-09-04T20:14:00.140354", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988040.1403549}, "user_action": true}, {"timestamp": "2025-09-04T20:14:03.124861", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988043.1248617}, "user_action": true}, {"timestamp": "2025-09-04T20:14:03.624633", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988043.6246336}, "user_action": true}, {"timestamp": "2025-09-04T20:14:08.234354", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988048.2343543}, "user_action": true}, {"timestamp": "2025-09-04T20:14:08.734326", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988048.7343264}, "user_action": true}, {"timestamp": "2025-09-04T20:14:10.747810", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988050.7462676}, "user_action": true}, {"timestamp": "2025-09-04T20:14:11.236085", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988051.232988}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.624917", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988064.3906512}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.624917", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.4063168}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.624917", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.4063168}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.640605", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.4063168}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.640605", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.4391778}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.640605", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.4391778}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.656248", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.4528883}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.656248", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.4528883}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.656248", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.4856915}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.671942", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.4856915}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.671942", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.499917}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.671942", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.499917}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.687629", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.499917}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.687629", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.531327}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.687629", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.531327}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.703700", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.5469694}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.703700", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.5627127}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.703700", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.5627127}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.718903", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.578346}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.718903", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.593566}, "user_action": true}, {"timestamp": "2025-09-04T20:14:24.718903", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.593566}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.281279", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988064.7345588}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.296939", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.781095}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.296939", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.828154}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.296939", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.8595233}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.312619", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.8903964}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.312619", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988064.9379556}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.312619", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988064.9685574}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.328319", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.0156896}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.343495", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.0313158}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.343495", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.062722}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.343495", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.062722}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.359175", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.0935729}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.359175", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.1249557}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.374850", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.1249557}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.374850", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.1563423}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.374850", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.1720262}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.390583", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.1906161}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.390583", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988065.2028954}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.406251", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988065.2185833}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.406251", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988065.2342556}, "user_action": true}, {"timestamp": "2025-09-04T20:14:25.421923", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988065.2655878}, "user_action": true}, {"timestamp": "2025-09-04T20:15:11.573526", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988111.5735269}, "user_action": true}, {"timestamp": "2025-09-04T20:15:12.073712", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988112.0737123}, "user_action": true}, {"timestamp": "2025-09-04T20:15:14.354782", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988114.3547828}, "user_action": true}, {"timestamp": "2025-09-04T20:15:14.854868", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988114.8548682}, "user_action": true}, {"timestamp": "2025-09-04T20:15:16.901626", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988116.901626}, "user_action": true}, {"timestamp": "2025-09-04T20:15:17.401814", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988117.4018142}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.248424", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988127.87343}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.264150", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988127.87343}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.264150", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988127.9047852}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.279789", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988127.9047852}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.279789", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988127.9365745}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.295450", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988127.9517813}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.311130", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988127.9733057}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.311130", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988127.9826417}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.326841", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988127.9983103}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.326841", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.0149655}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.342008", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.053678}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.342008", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.0610502}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.357655", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.0926626}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.357655", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.0926626}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.373318", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.1244705}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.373318", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.1244705}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.388987", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.1391551}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.388987", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.1704996}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.404665", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.2018588}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.420318", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.2018588}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.420318", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.2327557}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.842448", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988128.4359818}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.857625", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988128.4673052}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.873302", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988128.4673052}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.888992", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988128.4986165}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.888992", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988128.5142834}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.904657", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988128.5299296}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.904657", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.5607936}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.920799", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.5607936}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.920799", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.592098}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.936006", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.607807}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.951689", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.6234827}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.951689", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.6548498}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.967374", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.6705258}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.983029", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.6861794}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.983029", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7013917}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.998709", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7170384}, "user_action": true}, {"timestamp": "2025-09-04T20:15:28.998709", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7483804}, "user_action": true}, {"timestamp": "2025-09-04T20:15:29.014863", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7483804}, "user_action": true}, {"timestamp": "2025-09-04T20:15:29.014863", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7797196}, "user_action": true}, {"timestamp": "2025-09-04T20:15:29.029531", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.7954037}, "user_action": true}, {"timestamp": "2025-09-04T20:15:29.029531", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988128.8110905}, "user_action": true}, {"timestamp": "2025-09-04T20:16:09.746962", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988169.746962}, "user_action": true}, {"timestamp": "2025-09-04T20:16:10.262297", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988170.2622976}, "user_action": true}, {"timestamp": "2025-09-04T20:16:13.465612", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988173.4656126}, "user_action": true}, {"timestamp": "2025-09-04T20:16:13.965273", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988173.9652731}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.013372", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988177.637059}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.013372", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.6527195}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.039309", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.684033}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.043782", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.7001946}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.058981", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.7153783}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.058981", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.7467504}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.074650", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.7467504}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.090310", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988177.7624586}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.105969", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.7938373}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.105969", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.7938373}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.137800", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.809019}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.137800", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.8404224}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.152967", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.8404224}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.168615", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.8742404}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.185741", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.8874655}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.199926", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.921721}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.199926", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.921721}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.215612", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.9340026}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.231285", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.9673166}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.231285", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.9673166}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.246472", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988177.981053}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.684160", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988178.2639585}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.699866", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.278163}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.715565", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.278163}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.731226", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.3089633}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.746895", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.3417943}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.762098", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.356482}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.762098", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.356482}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.786056", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988178.392219}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.793405", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.4029982}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.809063", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.4029982}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.824732", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.4343727}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.840409", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.4656622}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.856090", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.4965808}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.856090", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.5279288}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.871751", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.5279288}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.887419", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.559284}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.903083", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.5906408}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.918745", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.605827}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.934397", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.6214674}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.949561", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.6527886}, "user_action": true}, {"timestamp": "2025-09-04T20:16:18.965224", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988178.6527886}, "user_action": true}, {"timestamp": "2025-09-04T20:16:54.401191", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988214.4011915}, "user_action": true}, {"timestamp": "2025-09-04T20:16:54.901262", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988214.9012625}, "user_action": true}, {"timestamp": "2025-09-04T20:16:56.744798", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988216.7447982}, "user_action": true}, {"timestamp": "2025-09-04T20:16:57.244986", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988217.2449865}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.089100", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988224.6981087}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.104293", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988224.6981087}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.135668", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988224.72944}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.151362", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988224.72944}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.166564", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988224.760281}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.182235", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988224.7759862}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.197917", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.8234534}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.213606", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.8386526}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.229275", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.870049}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.244956", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.870049}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.276295", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.901402}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.276295", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.933722}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.307176", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.933722}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.307176", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.947929}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.338554", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.9792943}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.338554", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988224.9949875}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.369930", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.0106506}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.369930", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.026361}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.401301", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.0435114}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.416992", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.0572538}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.432172", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.0572538}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.869808", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988225.4478738}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.885478", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988225.4792356}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.901147", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988225.4949148}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.916806", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988225.5106256}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.932496", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988225.5419471}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.948148", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988225.5419471}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.963790", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.5733554}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.978964", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.6041996}, "user_action": true}, {"timestamp": "2025-09-04T20:17:05.997562", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.6041996}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.010296", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.6355276}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.025991", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.651239}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.043601", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.668386}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.057310", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.6821196}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.072957", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.6821196}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.104270", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.7134688}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.119955", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.744809}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.135625", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.7604685}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.151261", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.7761805}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.166947", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.808064}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.182134", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.808064}, "user_action": true}, {"timestamp": "2025-09-04T20:17:06.197773", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988225.8389258}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.540940", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988270.1036668}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.556616", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.119348}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.587932", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.1516974}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.603576", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.1516974}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.619264", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.1659245}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.650573", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.2001178}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.666263", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.212839}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.682095", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.2285435}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.697292", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.244233}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.712973", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.275557}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.728668", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.3073425}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.744339", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.3225088}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.775718", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.3381736}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.790894", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.3695004}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.806582", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4008517}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.822262", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4008517}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.837957", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4317307}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.854107", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4650543}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.884945", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4787686}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.900615", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.4787686}, "user_action": true}, {"timestamp": "2025-09-04T20:17:50.916291", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988270.5100794}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.369104", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988270.9319732}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.384807", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.9629571}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.400480", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.9786506}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.416142", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988270.994308}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.447506", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988271.0257013}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.463172", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988271.0409098}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.478821", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.0565732}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.493992", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.072243}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.525338", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.1035554}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.541028", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.119203}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.556671", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.1505442}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.587983", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.1505442}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.603702", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.1818857}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.619330", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.197584}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.634987", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.213229}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.650632", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.244104}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.682000", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.2598374}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.697175", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.2755146}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.712839", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.3068619}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.728518", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.3068619}, "user_action": true}, {"timestamp": "2025-09-04T20:17:51.759888", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988271.3382325}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.593921", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988300.9883683}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.617221", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988301.008057}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.640795", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988301.0283968}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.662227", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988301.063768}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.685606", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988301.1006317}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.708037", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988301.1223874}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.731479", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.1526906}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.752045", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.18291}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.774569", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.2043717}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.799099", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.238375}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.823573", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.2601008}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.912021", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.2933311}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.931758", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.3261945}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.952451", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.353922}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.975481", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.387087}, "user_action": true}, {"timestamp": "2025-09-04T20:18:21.996062", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.414109}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.026426", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.446439}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.049013", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.47569}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.072529", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.50118}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.094982", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.5344808}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.116149", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988301.5666516}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.770521", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988302.137683}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.797886", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988302.1796172}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.822521", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988302.2121747}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.849479", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988302.235339}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.877197", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988302.2675462}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.899357", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988302.2941947}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.922910", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.3271534}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.947142", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.357522}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.967711", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.384918}, "user_action": true}, {"timestamp": "2025-09-04T20:18:22.989252", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.416232}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.013800", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.4415407}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.037252", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.4767272}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.058623", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.505994}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.082001", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.5284507}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.104490", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.5656633}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.125896", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.5889146}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.148480", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.6199837}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.172142", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.6405594}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.203208", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.6758375}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.228748", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.7080324}, "user_action": true}, {"timestamp": "2025-09-04T20:18:23.250070", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988302.7304978}, "user_action": true}, {"timestamp": "2025-09-04T20:18:59.741820", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988339.7418206}, "user_action": true}, {"timestamp": "2025-09-04T20:19:00.242040", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988340.2420402}, "user_action": true}, {"timestamp": "2025-09-04T20:19:02.617135", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988342.6171353}, "user_action": true}, {"timestamp": "2025-09-04T20:19:03.132610", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988343.1326103}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.304520", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988346.8825834}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.320190", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988346.8984604}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.351488", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988346.9136581}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.382304", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988346.9293287}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.397966", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988346.9608717}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.429296", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988346.9765658}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.460617", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.0074399}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.476266", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.0231452}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.507666", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.0231452}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.523331", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.0701985}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.554642", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.0701985}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.585459", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.1015515}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.601119", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.132454}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.632484", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.1481256}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.648153", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.1638303}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.679510", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.1795177}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.710863", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.1951983}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.726518", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.2260814}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.757365", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.2417948}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.788708", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.2594187}, "user_action": true}, {"timestamp": "2025-09-04T20:19:07.804393", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.2731364}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.307666", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988347.835724}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.319904", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.8670392}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.351216", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.8670392}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.382555", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.8983622}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.413881", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.9140205}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.445208", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988347.9291947}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.460856", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.9605482}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.491685", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.9919393}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.523046", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988347.9919393}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.538718", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.0232718}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.570066", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.0545933}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.601386", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.0545933}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.617050", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.085908}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.648382", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.1167097}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.663559", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.1480277}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.694867", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.179333}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.726219", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.179333}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.741871", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.210659}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.773182", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988348.226334}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.788857", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988348.2420096}, "user_action": true}, {"timestamp": "2025-09-04T20:19:08.820207", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988348.2733736}, "user_action": true}, {"timestamp": "2025-09-04T20:19:39.866503", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988379.8665037}, "user_action": true}, {"timestamp": "2025-09-04T20:19:40.366624", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988380.3666244}, "user_action": true}, {"timestamp": "2025-09-04T20:19:42.302813", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988382.2871199}, "user_action": true}, {"timestamp": "2025-09-04T20:19:42.802990", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988382.8029902}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.771649", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988386.3655598}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.802988", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988386.3812108}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.834313", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988386.412528}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.865642", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988386.4438553}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.896500", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988386.4594944}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.927811", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988386.4746618}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.959129", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.506757}, "user_action": true}, {"timestamp": "2025-09-04T20:19:46.990451", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.506757}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.021790", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.52144}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.037430", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.5537336}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.068738", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.5684054}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.099571", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.5684054}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.130884", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.6021926}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.162195", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.6154082}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.177846", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.6310632}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.209155", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.64769}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.240498", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.6780317}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.256182", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.6780317}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.287501", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.709407}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.318406", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.7245994}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.349744", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988386.7402894}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.834094", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988387.3967624}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.865452", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988387.428111}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.896809", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988387.4438019}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.928298", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988387.45899}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.959145", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988387.4903555}, "user_action": true}, {"timestamp": "2025-09-04T20:19:47.990467", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988387.5065749}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.021812", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.521768}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.053190", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.5566688}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.084031", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.57147}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.099688", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.5842166}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.131014", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.599899}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.162363", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.6312747}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.193714", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.6312747}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.225055", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.6621644}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.255877", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.693482}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.271539", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.7091897}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.302850", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.7258515}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.334168", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.7562456}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.365487", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.7562456}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.381125", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.7562456}, "user_action": true}, {"timestamp": "2025-09-04T20:19:48.412434", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988387.8027508}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.067196", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756992738.2857413}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.098533", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992738.3016965}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.129400", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992738.3482485}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.160773", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992738.395263}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.192117", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992738.426647}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.223497", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992738.458014}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.254835", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.5068297}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.285651", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.5357478}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.316978", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.5827863}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.348313", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.6141167}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.379656", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.6464512}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.411010", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.692018}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.442341", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.7233827}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.473184", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.7547846}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.504547", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.8170135}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.535885", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.8483894}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.567247", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.8797874}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.598550", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.9106958}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.614221", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.9420846}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.645057", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992738.9734666}, "user_action": true}, {"timestamp": "2025-09-04T21:32:19.676366", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.0044694}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.239112", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756992739.72335}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.270454", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992739.7704437}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.301334", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992739.802334}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.332623", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992739.8484385}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.348270", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992739.87978}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.395244", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756992739.8967254}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.410893", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.9109542}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.442214", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.9266417}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.473574", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.9423115}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.504402", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.973178}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.535729", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992739.973178}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.567089", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.0045106}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.582755", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.0358088}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.614095", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.0358088}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.645452", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.0671496}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.677774", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.0985258}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.707630", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.1298602}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.738939", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.1298602}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.771732", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.1607587}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.801636", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.192126}, "user_action": true}, {"timestamp": "2025-09-04T21:32:20.832949", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756992740.2234643}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.110538", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1757000854.6948917}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.141839", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000854.6948917}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.188377", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000854.7262297}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.224125", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000854.7414124}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.251061", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000854.7571046}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.298040", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000854.7727716}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.329394", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8046854}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.360747", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8046854}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.407248", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8355298}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.438565", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8355298}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.469882", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8668535}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.516842", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.8914428}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.547659", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.9071057}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.594641", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.922805}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.625942", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.9551332}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.672937", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.9551332}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.719917", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000854.98555}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.751245", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000855.0012236}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.782061", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000855.0169237}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.829038", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000855.047822}, "user_action": true}, {"timestamp": "2025-09-04T23:47:35.860344", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000855.0791812}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.360442", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1757000855.907386}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.407458", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9387834}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.454051", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9387834}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.485368", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9696167}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.532369", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000856.0010135}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.563708", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000856.0166526}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.610265", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "field_2025年岗位工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0323358}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.735242", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "field_2025年校龄工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.063688}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.797931", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0949934}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.845391", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0949934}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.891882", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "field_2025年基础性绩效", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1258721}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.932517", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.157233}, "user_action": true}, {"timestamp": "2025-09-04T23:47:36.970304", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "field_2025年生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1729083}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.016851", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1885595}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.047668", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "field_2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2199302}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.094677", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2199302}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.129966", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2527637}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.173057", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2664475}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.203895", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2977984}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.250883", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2977984}, "user_action": true}, {"timestamp": "2025-09-04T23:47:37.282675", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.3291311}, "user_action": true}], "field_configs": {"序号": {"field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1757000855.907386}, "工号": {"field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9387834}, "姓名": {"field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9387834}, "部门名称": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000855.9696167}, "人员类别": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000856.0010135}, "人员类别代码": {"field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000856.0166526}, "2025年岗位工资": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0323358}, "2025年校龄工资": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.063688}, "津贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0949934}, "结余津贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.0949934}, "2025年基础性绩效": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1258721}, "卫生费": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.157233}, "2025年生活补贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1729083}, "车补": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.1885595}, "2025年奖励性绩效预发": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2199302}, "补发": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2199302}, "借支": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2527637}, "应发工资": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2664475}, "2025公积金": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2977984}, "保险扣款": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.2977984}, "代扣代存养老保险": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000856.3291311}}}, "mapping_config_离休人员工资表": {"metadata": {"created_at": "2025-09-04T20:14:31.343592", "last_modified": "2025-09-04T23:47:44.350872", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本离休费": "基本离休费", "结余津贴": "结余津贴", "生活补贴": "生活补贴", "住房补贴": "住房补贴", "物业补贴": "物业补贴", "离休补贴": "离休补贴", "护理费": "护理费", "增发一次性生活补贴": "增发一次性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": [{"timestamp": "2025-09-04T20:14:31.343592", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988071.3435926}, "user_action": true}, {"timestamp": "2025-09-04T20:14:31.843756", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988071.843756}, "user_action": true}, {"timestamp": "2025-09-04T20:14:50.076897", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988090.0768979}, "user_action": true}, {"timestamp": "2025-09-04T20:14:50.577023", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988090.577023}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.780288", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.798391", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.798391", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.798391", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.813129", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.813129", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.733244}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.826816", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7489345}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.842493", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7489345}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.842493", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.842493", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.858170", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.858170", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.873947", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.873947", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.889603", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:14:59.889603", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.7646148}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.215123", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988099.9096806}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.225994", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.9224267}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.234751", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.9224267}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.242367", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988099.9523091}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.250233", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.9523091}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.258059", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988099.9832046}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.265767", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.0187507}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.274707", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.0364444}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.282450", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.0539348}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.289210", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.0783162}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.297023", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.0997722}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.305870", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988100.1176267}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.313667", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988100.1381493}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.322332", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988100.1594527}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.329565", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988100.1779306}, "user_action": true}, {"timestamp": "2025-09-04T20:15:00.339903", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988100.19635}, "user_action": true}, {"timestamp": "2025-09-04T20:15:40.247930", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988140.2479303}, "user_action": true}, {"timestamp": "2025-09-04T20:15:40.748084", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988140.748085}, "user_action": true}, {"timestamp": "2025-09-04T20:15:43.122857", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988143.122858}, "user_action": true}, {"timestamp": "2025-09-04T20:15:43.607301", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988143.6073012}, "user_action": true}, {"timestamp": "2025-09-04T20:15:46.169856", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988146.169856}, "user_action": true}, {"timestamp": "2025-09-04T20:15:46.669600", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988146.669601}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.769755", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988151.4581282}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.784297", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.4668975}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.794169", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.4823084}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.804983", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.500108}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.819117", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.522525}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.830277", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.5539458}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.841703", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.5715446}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.852605", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.587869}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.863361", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.6053622}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.875200", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.6252587}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.884602", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.645372}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.896361", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.6681201}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.907152", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.685569}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.916859", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.7020671}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.928589", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988151.728743}, "user_action": true}, {"timestamp": "2025-09-04T20:15:51.938450", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.7501557}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.279305", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988151.9492285}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.292789", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.9717183}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.304729", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988151.991048}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.315434", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988152.0095663}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.327968", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.0313437}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.339995", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.0476549}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.351340", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.0859048}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.362219", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1026063}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.373188", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1228788}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.381854", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1397243}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.391585", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1561787}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.401286", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1736476}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.414052", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.1904087}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.426860", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.2090144}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.438549", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988152.2266734}, "user_action": true}, {"timestamp": "2025-09-04T20:15:52.451117", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988152.2500541}, "user_action": true}, {"timestamp": "2025-09-04T20:16:36.480301", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988196.4803014}, "user_action": true}, {"timestamp": "2025-09-04T20:16:36.995604", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988196.9956045}, "user_action": true}, {"timestamp": "2025-09-04T20:16:40.390011", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988200.387943}, "user_action": true}, {"timestamp": "2025-09-04T20:16:40.896569", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988200.8919964}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.685909", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988203.3790817}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.700053", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.4003131}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.715057", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.4171295}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.729310", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.4375513}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.746341", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.456271}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.758674", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.4735148}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.775546", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.49538}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.789309", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.511673}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.805724", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.5303302}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.819456", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.5498898}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.833131", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.5656652}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.845761", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.589102}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.861221", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.6047432}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.873936", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.6258702}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.890851", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988203.64479}, "user_action": true}, {"timestamp": "2025-09-04T20:16:43.908083", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.661267}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.239185", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988203.922858}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.256934", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.941674}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.270512", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.967687}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.291147", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988203.9872296}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.306482", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988204.0058124}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.322118", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988204.0225058}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.337750", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.0449443}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.351750", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.0636609}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.367402", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.0919306}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.380731", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.108514}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.398451", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.1250556}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.411235", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.14264}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.425824", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.1661706}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.440240", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.1825888}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.455270", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988204.2001286}, "user_action": true}, {"timestamp": "2025-09-04T20:16:44.467749", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988204.218968}, "user_action": true}, {"timestamp": "2025-09-04T20:17:31.830861", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988251.8278074}, "user_action": true}, {"timestamp": "2025-09-04T20:17:32.342665", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988252.3375046}, "user_action": true}, {"timestamp": "2025-09-04T20:17:35.134264", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988255.131217}, "user_action": true}, {"timestamp": "2025-09-04T20:17:35.633131", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988255.6282847}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.386943", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988258.1504765}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.400663", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.1661503}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.416345", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.1661503}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.435007", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.1980429}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.447225", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.1980429}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.462884", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.2294178}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.480510", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2294178}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.510889", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2441316}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.525566", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2598116}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.541227", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2754827}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.556879", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2911909}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.572554", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.2911909}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.588256", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.3225677}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.603940", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.3225677}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.619105", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.3541675}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.634755", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.3541675}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.885104", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988258.650414}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.916047", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.6660933}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.931910", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.697459}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.950010", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.697459}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.963241", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.7287865}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.978923", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.7444782}, "user_action": true}, {"timestamp": "2025-09-04T20:17:38.994072", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.760158}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.009731", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.760158}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.027824", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.7909756}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.041035", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.7909756}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.072347", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.8223782}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.088010", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.8223782}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.103731", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.8223782}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.119433", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.8576415}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.135068", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988258.8694372}, "user_action": true}, {"timestamp": "2025-09-04T20:17:39.150719", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988258.8694372}, "user_action": true}, {"timestamp": "2025-09-04T20:18:02.711801", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988282.7118015}, "user_action": true}, {"timestamp": "2025-09-04T20:18:03.212147", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988283.2121472}, "user_action": true}, {"timestamp": "2025-09-04T20:18:05.571249", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988285.571249}, "user_action": true}, {"timestamp": "2025-09-04T20:18:06.071456", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988286.071456}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.789527", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988292.5550702}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.820410", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.570737}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.836076", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.570737}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.836076", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.602528}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.867394", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.602528}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.883063", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.6172132}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.914364", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.6172132}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.930017", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.6539009}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.947628", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.668576}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.961350", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.668576}, "user_action": true}, {"timestamp": "2025-09-04T20:18:12.992219", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.695543}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.007865", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.695543}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.023543", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.7268674}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.057350", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.7268674}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.070555", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988292.7582107}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.086224", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988292.7582107}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.367483", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988293.1018968}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.383144", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.1175442}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.398810", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.1332383}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.430146", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.148902}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.445323", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.164095}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.476702", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.1954565}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.492388", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.1954565}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.508078", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.2111459}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.540854", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.226829}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.555049", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.226829}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.570733", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.2425175}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.601590", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.2738826}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.617253", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.2890809}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.649558", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.3047569}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.664256", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988293.3204463}, "user_action": true}, {"timestamp": "2025-09-04T20:18:13.695573", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988293.3361046}, "user_action": true}, {"timestamp": "2025-09-04T20:18:37.537366", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988317.537366}, "user_action": true}, {"timestamp": "2025-09-04T20:18:38.053151", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988318.0531511}, "user_action": true}, {"timestamp": "2025-09-04T20:18:40.443841", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988320.4438412}, "user_action": true}, {"timestamp": "2025-09-04T20:18:40.943862", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988320.9438622}, "user_action": true}, {"timestamp": "2025-09-04T20:18:44.133310", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988324.1176298}, "user_action": true}, {"timestamp": "2025-09-04T20:18:44.617762", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988324.6177626}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.164398", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988329.9301157}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.196230", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988329.9457943}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.211391", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988329.9619725}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.242708", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988329.977178}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.258356", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988329.9926097}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.289222", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988329.9926097}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.304864", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0251687}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.336175", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0393891}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.351838", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0550542}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.383201", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0707278}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.398885", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0864167}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.430261", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.0864167}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.445948", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.1020958}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.476837", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.1020958}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.492503", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.149685}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.539455", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.149685}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.789948", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988330.5551243}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.820768", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.570766}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.852084", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.5864635}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.867734", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.5864635}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.898620", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6178389}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.914302", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6178389}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.945657", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6330454}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.961318", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6644506}, "user_action": true}, {"timestamp": "2025-09-04T20:18:50.977001", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6644506}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.008294", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6644506}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.023983", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.6959}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.055320", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988330.7115252}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.070497", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.7267306}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.101849", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.7267306}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.133173", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.7580862}, "user_action": true}, {"timestamp": "2025-09-04T20:18:51.148830", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988330.7580862}, "user_action": true}, {"timestamp": "2025-09-04T20:19:15.471852", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988355.468278}, "user_action": true}, {"timestamp": "2025-09-04T20:19:15.984445", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988355.9787138}, "user_action": true}, {"timestamp": "2025-09-04T20:19:17.936177", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988357.9361775}, "user_action": true}, {"timestamp": "2025-09-04T20:19:18.436399", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988358.4363997}, "user_action": true}, {"timestamp": "2025-09-04T20:19:20.686605", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988360.686606}, "user_action": true}, {"timestamp": "2025-09-04T20:19:21.186180", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988361.1861808}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.241502", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988368.9913018}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.272821", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.0070016}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.304133", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.0388293}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.319819", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.0388293}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.350652", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.0750735}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.381988", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.085384}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.413308", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.085384}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.428951", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.085384}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.460269", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1167293}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.475931", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1167293}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.507256", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1480496}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.522925", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1480496}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.554233", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.179851}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.585095", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1945262}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.616453", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.1945262}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.632141", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.2273037}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.913307", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988369.6635213}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.928988", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.6791966}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.960335", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.694855}, "user_action": true}, {"timestamp": "2025-09-04T20:19:29.991669", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.7100294}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.023056", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.72575}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.038230", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.7413654}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.069552", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.7570584}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.100866", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.7727296}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.116524", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.7727296}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.147857", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.8040652}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.179199", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.8040652}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.210054", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.8354256}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.225737", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.8354256}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.272766", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.866785}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.288454", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988369.866785}, "user_action": true}, {"timestamp": "2025-09-04T20:19:30.319774", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988369.897637}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.037024", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988394.8029377}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.074189", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988394.818631}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.101588", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988394.818631}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.131438", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988394.8342936}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.162334", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.8504868}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.177971", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.865191}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.209310", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.881862}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.240149", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.8965423}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.334137", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.8965423}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.365474", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.9122002}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.381129", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.9435475}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.428101", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.9592218}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.443293", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.9778128}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.474649", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.990541}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.505944", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988394.990541}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.537259", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988395.0228283}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.818720", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756988395.5685976}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.849550", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988395.5842803}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.880874", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988395.5999396}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.912251", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988395.615633}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.943572", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.6307986}, "user_action": true}, {"timestamp": "2025-09-04T20:19:55.974939", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.6464584}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.005767", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.6621418}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.037111", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.693491}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.068443", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.693491}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.099788", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7091393}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.131124", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7247863}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.146812", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7404509}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.177636", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7561002}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.224619", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7717638}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.271606", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756988395.7874103}, "user_action": true}, {"timestamp": "2025-09-04T20:19:56.302918", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756988395.8030791}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.286470", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756994248.0521884}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.318278", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.0678673}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.349131", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.0678673}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.380470", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.099228}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.418651", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.099228}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.458289", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.114915}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.489615", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.1305733}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.520928", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.1463864}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.536568", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.163045}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.645730", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.1797051}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.661378", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.192959}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.692691", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.192959}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.724005", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.2242813}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.755374", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.239466}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.787705", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.2551887}, "user_action": true}, {"timestamp": "2025-09-04T21:57:28.833250", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.2551887}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.130101", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1756994248.8489552}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.161412", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.8802755}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.192724", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.8802755}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.224034", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994248.911622}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.255349", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.9272864}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.286687", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.9429657}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.318027", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.9429657}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.364507", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.974325}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.395816", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994248.9894993}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.427171", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0051615}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.458492", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0208411}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.489889", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0365136}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.520695", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0365136}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.552007", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0678895}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.614701", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994249.0678895}, "user_action": true}, {"timestamp": "2025-09-04T21:57:29.646082", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994249.0992682}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.343521", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1757000827.8749566}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.374859", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000827.8906405}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.421924", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000827.9220116}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.468879", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000827.9690251}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.499715", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.0004137}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.531030", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.0313}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.562340", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.0626662}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.609401", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.0778494}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.640721", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.1092439}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.687256", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.1249144}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.718624", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.156287}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.749951", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.1876616}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.796972", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.2185397}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.836234", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.2499292}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.867576", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000828.2812726}, "user_action": true}, {"timestamp": "2025-09-04T23:47:08.898906", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000828.3126523}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.414693", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "integer", "data_type": "INT", "is_required": false, "last_modified": 1757000828.945905}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.445592", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000828.9615927}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.492600", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000828.992488}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.524006", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000829.0238817}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.571002", "field": "基本离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.0552273}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.617561", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.086577}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.648880", "field": "生活补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.1174567}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.695841", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.1331778}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.727191", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.1801705}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.758490", "field": "离休补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.1958241}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.805032", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.227154}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.836377", "field": "增发一次性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.2584398}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.867683", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.2892907}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.914657", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.3206143}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.945982", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1757000829.3519905}, "user_action": true}, {"timestamp": "2025-09-04T23:47:09.976781", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1757000829.3833416}, "user_action": true}]}, "mapping_config_退休人员工资表": {"metadata": {"created_at": "2025-09-04T21:57:33.083428", "last_modified": "2025-09-04T21:57:56.511706", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "field_2016待遇调整", "2017待遇调整": "field_2017待遇调整", "2018待遇调整": "field_2018待遇调整", "2019待遇调整": "field_2019待遇调整", "2020待遇调整": "field_2020待遇调整", "2021待遇调整": "field_2021待遇调整", "2022待遇调整": "field_2022待遇调整", "2023待遇调整": "field_2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": [{"timestamp": "2025-09-04T21:57:54.090007", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.042969}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.123306", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "code_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.153652", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.183563", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.230042", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.261381", "field": "基本退休费", "action": "field_config_update", "config": {"target_field": "基本退休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.295176", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.324057", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.355392", "field": "离退休生活补贴", "action": "field_config_update", "config": {"target_field": "离退休生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.388167", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.418060", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.448900", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.480209", "field": "增资预付", "action": "field_config_update", "config": {"target_field": "增资预付", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.511566", "field": "2016待遇调整", "action": "field_config_update", "config": {"target_field": "field_2016待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.542881", "field": "2017待遇调整", "action": "field_config_update", "config": {"target_field": "field_2017待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.574198", "field": "2018待遇调整", "action": "field_config_update", "config": {"target_field": "field_2018待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.620751", "field": "2019待遇调整", "action": "field_config_update", "config": {"target_field": "field_2019待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.652077", "field": "2020待遇调整", "action": "field_config_update", "config": {"target_field": "field_2020待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.683416", "field": "2021待遇调整", "action": "field_config_update", "config": {"target_field": "field_2021待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.058652}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.730406", "field": "2022待遇调整", "action": "field_config_update", "config": {"target_field": "field_2022待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0738666}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.761785", "field": "2023待遇调整", "action": "field_config_update", "config": {"target_field": "field_2023待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0738666}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.792641", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0738666}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.823952", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0738666}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.855284", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994274.0738666}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.886627", "field": "公积", "action": "field_config_update", "config": {"target_field": "公积", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0900073}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.917936", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0900073}, "user_action": true}, {"timestamp": "2025-09-04T21:57:54.964449", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.0900073}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.605200", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.9958272}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.636570", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "code_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.027169}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.683580", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.058483}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.714451", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.0741534}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.745794", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.0898263}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.777145", "field": "基本退休费", "action": "field_config_update", "config": {"target_field": "基本退休费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.120694}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.824146", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.120694}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.839817", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1520388}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.886304", "field": "离退休生活补贴", "action": "field_config_update", "config": {"target_field": "离退休生活补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1833513}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.917617", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1990376}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.948924", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.2147105}, "user_action": true}, {"timestamp": "2025-09-04T21:57:55.980283", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.246034}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.011611", "field": "增资预付", "action": "field_config_update", "config": {"target_field": "增资预付", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.2773921}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.042951", "field": "2016待遇调整", "action": "field_config_update", "config": {"target_field": "field_2016待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3082848}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.089495", "field": "2017待遇调整", "action": "field_config_update", "config": {"target_field": "field_2017待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3082848}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.120873", "field": "2018待遇调整", "action": "field_config_update", "config": {"target_field": "field_2018待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3396351}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.152690", "field": "2019待遇调整", "action": "field_config_update", "config": {"target_field": "field_2019待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.370992}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.199207", "field": "2020待遇调整", "action": "field_config_update", "config": {"target_field": "field_2020待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.40237}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.230580", "field": "2021待遇调整", "action": "field_config_update", "config": {"target_field": "field_2021待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.40237}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.261479", "field": "2022待遇调整", "action": "field_config_update", "config": {"target_field": "field_2022待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4332085}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.293333", "field": "2023待遇调整", "action": "field_config_update", "config": {"target_field": "field_2023待遇调整", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4645953}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.324210", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4803014}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.370698", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.498955}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.402997", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.511673}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.433409", "field": "公积", "action": "field_config_update", "config": {"target_field": "公积", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5273192}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.464733", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5429897}, "user_action": true}, {"timestamp": "2025-09-04T21:57:56.511706", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5738554}, "user_action": true}], "field_configs": {"序号": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994274.9958272}, "人员代码": {"field_type": "code_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.027169}, "姓名": {"field_type": "name_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.058483}, "部门名称": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.0741534}, "人员类别代码": {"field_type": "personnel_category_code", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.0898263}, "基本退休费": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.120694}, "津贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.120694}, "结余津贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1520388}, "离退休生活补贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1833513}, "护理费": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.1990376}, "物业补贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.2147105}, "住房补贴": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.246034}, "增资预付": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.2773921}, "2016待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3082848}, "2017待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3082848}, "2018待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.3396351}, "2019待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.370992}, "2020待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.40237}, "2021待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.40237}, "2022待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4332085}, "2023待遇调整": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4645953}, "补发": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.4803014}, "借支": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.498955}, "应发工资": {"field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994275.511673}, "公积": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5273192}, "保险扣款": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5429897}, "备注": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994275.5738554}}}, "mapping_config_全部在职人员工资表": {"metadata": {"created_at": "2025-09-04T21:58:18.604896", "last_modified": "2025-09-04T21:58:40.545857", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-09-04T21:58:35.530283", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994315.530283}, "user_action": true}, {"timestamp": "2025-09-04T21:58:36.030006", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "field_2025公积金", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994316.030006}, "user_action": true}, {"timestamp": "2025-09-04T21:58:37.874059", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "VARCHAR(100)", "is_required": false, "last_modified": 1756994317.874059}, "user_action": true}, {"timestamp": "2025-09-04T21:58:38.373720", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "DECIMAL(10,2)", "is_required": false, "last_modified": 1756994318.3737202}, "user_action": true}]}}, "field_templates": {"离休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "退休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "全部在职人员工资表": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "A岗职工": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}}, "user_preferences": {"default_field_patterns": {}, "recent_edits": [], "favorite_mappings": []}}